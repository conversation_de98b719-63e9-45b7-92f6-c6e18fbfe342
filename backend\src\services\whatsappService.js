const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode');
const path = require('path');
const fs = require('fs').promises;
const logger = require('../utils/logger');

class WhatsAppService {
  constructor(io) {
    this.io = io;
    this.client = null;
    this.qrCode = null;
    this.isClientReady = false;
    this.isClientAuthenticated = false;
    this.sessionPath = process.env.WHATSAPP_SESSION_PATH || './sessions';
    this.initializeClient();
  }

  initializeClient() {
    this.client = new Client({
      authStrategy: new LocalAuth({
        dataPath: this.sessionPath
      }),
      puppeteer: {
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ]
      }
    });

    this.setupEventListeners();
  }

  setupEventListeners() {
    // QR Code event
    this.client.on('qr', async (qr) => {
      logger.info('QR Code received');
      try {
        // Store the raw QR string instead of converting to data URL
        // The frontend QRCode component expects the raw string
        this.qrCode = qr;
        this.io.emit('qr', this.qrCode);
      } catch (error) {
        logger.error('Error generating QR code:', error);
      }
    });

    // Authentication events
    this.client.on('authenticated', () => {
      logger.info('WhatsApp client authenticated');
      this.isClientAuthenticated = true;
      this.qrCode = null;
      this.io.emit('authenticated');
    });

    this.client.on('auth_failure', (msg) => {
      logger.error('Authentication failed:', msg);
      this.isClientAuthenticated = false;
      this.io.emit('auth_failure', msg);
    });

    // Ready event
    this.client.on('ready', () => {
      logger.info('WhatsApp client is ready');
      this.isClientReady = true;
      this.io.emit('ready');
    });

    // Message events
    this.client.on('message', (message) => {
      logger.info(`New message from ${message.from}: ${message.body}`);
      this.io.emit('message', {
        id: message.id._serialized,
        from: message.from,
        to: message.to,
        body: message.body,
        type: message.type,
        timestamp: message.timestamp,
        fromMe: message.fromMe,
        hasMedia: message.hasMedia
      });
    });

    this.client.on('message_create', (message) => {
      this.io.emit('message_create', {
        id: message.id._serialized,
        from: message.from,
        to: message.to,
        body: message.body,
        type: message.type,
        timestamp: message.timestamp,
        fromMe: message.fromMe,
        hasMedia: message.hasMedia
      });
    });

    // Disconnection event
    this.client.on('disconnected', (reason) => {
      logger.warn('WhatsApp client disconnected:', reason);
      this.isClientReady = false;
      this.isClientAuthenticated = false;
      this.io.emit('disconnected', reason);
    });

    // State change event
    this.client.on('change_state', (state) => {
      logger.info('WhatsApp client state changed:', state);
      this.io.emit('change_state', state);
    });
  }

  async initialize() {
    try {
      // Ensure session directory exists
      await fs.mkdir(this.sessionPath, { recursive: true });
      
      logger.info('Initializing WhatsApp client...');
      await this.client.initialize();
    } catch (error) {
      logger.error('Failed to initialize WhatsApp client:', error);
      throw error;
    }
  }

  async sendMessage(chatId, content, options = {}) {
    if (!this.isClientReady) {
      throw new Error('WhatsApp client is not ready');
    }

    try {
      const message = await this.client.sendMessage(chatId, content, options);
      logger.info(`Message sent to ${chatId}`);
      return {
        id: message.id._serialized,
        from: message.from,
        to: message.to,
        body: message.body,
        type: message.type,
        timestamp: message.timestamp,
        ack: message.ack
      };
    } catch (error) {
      logger.error('Error sending message:', error);
      throw error;
    }
  }

  async sendMedia(chatId, media, options = {}) {
    if (!this.isClientReady) {
      throw new Error('WhatsApp client is not ready');
    }

    try {
      const messageMedia = MessageMedia.fromFilePath(media.path);
      if (options.caption) {
        messageMedia.caption = options.caption;
      }
      
      const message = await this.client.sendMessage(chatId, messageMedia, options);
      logger.info(`Media message sent to ${chatId}`);
      return {
        id: message.id._serialized,
        from: message.from,
        to: message.to,
        type: message.type,
        timestamp: message.timestamp,
        ack: message.ack
      };
    } catch (error) {
      logger.error('Error sending media:', error);
      throw error;
    }
  }

  async getContacts() {
    if (!this.isClientReady) {
      throw new Error('WhatsApp client is not ready');
    }

    try {
      const contacts = await this.client.getContacts();
      return contacts.map(contact => ({
        id: contact.id._serialized,
        name: contact.name,
        pushname: contact.pushname,
        number: contact.number,
        isGroup: contact.isGroup,
        isUser: contact.isUser,
        isMyContact: contact.isMyContact
      }));
    } catch (error) {
      logger.error('Error getting contacts:', error);
      throw error;
    }
  }

  async getChats() {
    if (!this.isClientReady) {
      throw new Error('WhatsApp client is not ready');
    }

    try {
      const chats = await this.client.getChats();
      return chats.map(chat => ({
        id: chat.id._serialized,
        name: chat.name,
        isGroup: chat.isGroup,
        isReadOnly: chat.isReadOnly,
        unreadCount: chat.unreadCount,
        timestamp: chat.timestamp,
        archived: chat.archived,
        pinned: chat.pinned
      }));
    } catch (error) {
      logger.error('Error getting chats:', error);
      throw error;
    }
  }

  async getChatById(chatId) {
    if (!this.isClientReady) {
      throw new Error('WhatsApp client is not ready');
    }

    try {
      const chat = await this.client.getChatById(chatId);
      return {
        id: chat.id._serialized,
        name: chat.name,
        isGroup: chat.isGroup,
        isReadOnly: chat.isReadOnly,
        unreadCount: chat.unreadCount,
        timestamp: chat.timestamp,
        archived: chat.archived,
        pinned: chat.pinned
      };
    } catch (error) {
      logger.error('Error getting chat:', error);
      throw error;
    }
  }

  async getMessages(chatId, limit = 50) {
    if (!this.isClientReady) {
      throw new Error('WhatsApp client is not ready');
    }

    try {
      const chat = await this.client.getChatById(chatId);
      const messages = await chat.fetchMessages({ limit });
      
      return messages.map(message => ({
        id: message.id._serialized,
        from: message.from,
        to: message.to,
        body: message.body,
        type: message.type,
        timestamp: message.timestamp,
        fromMe: message.fromMe,
        hasMedia: message.hasMedia,
        ack: message.ack
      }));
    } catch (error) {
      logger.error('Error getting messages:', error);
      throw error;
    }
  }

  isReady() {
    return this.isClientReady;
  }

  isAuthenticated() {
    return this.isClientAuthenticated;
  }

  getQRCode() {
    return this.qrCode;
  }

  async logout() {
    if (this.client) {
      try {
        await this.client.logout();
        this.isClientReady = false;
        this.isClientAuthenticated = false;
        this.qrCode = null;
        logger.info('WhatsApp client logged out');
      } catch (error) {
        logger.error('Error logging out:', error);
        throw error;
      }
    }
  }

  async destroy() {
    if (this.client) {
      try {
        await this.client.destroy();
        this.isClientReady = false;
        this.isClientAuthenticated = false;
        this.qrCode = null;
        logger.info('WhatsApp client destroyed');
      } catch (error) {
        logger.error('Error destroying client:', error);
        throw error;
      }
    }
  }
}

module.exports = WhatsAppService;
