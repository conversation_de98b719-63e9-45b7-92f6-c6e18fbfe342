import { create } from 'zustand'
import toast from 'react-hot-toast'
import { useSocketStore } from './socketStore'

export const useWhatsAppStore = create((set, get) => ({
  // State
  isReady: false,
  isAuthenticated: false,
  qrCode: null,
  connectionState: 'DISCONNECTED',
  error: null,
  
  // Messages
  messages: [],
  currentChat: null,
  
  // Contacts and Chats
  contacts: [],
  chats: [],
  
  // Loading states
  isLoadingMessages: false,
  isLoadingContacts: false,
  isLoadingChats: false,
  
  // Actions
  initialize: () => {
    const socketStore = useSocketStore.getState()
    
    // Set up WhatsApp event listeners
    socketStore.on('whatsapp_status', (status) => {
      set({
        isReady: status.isReady,
        isAuthenticated: status.isAuthenticated,
        qrCode: status.hasQRCode ? null : get().qrCode, // Keep existing QR if available
        error: status.error || null
      })
    })

    socketStore.on('qr', (qrCode) => {
      console.log('QR Code received:', qrCode ? `${qrCode.length} characters` : 'null')
      set({ qrCode, isAuthenticated: false })
      toast('New QR code generated. Please scan with your phone.', {
        icon: '📱',
        duration: 5000
      })
    })

    socketStore.on('qr_response', (response) => {
      if (response.available && response.qrCode) {
        set({ qrCode: response.qrCode, isAuthenticated: false })
        toast('QR code refreshed. Please scan with your phone.', {
          icon: '📱',
          duration: 5000
        })
      } else {
        set({ qrCode: null })
        if (response.error) {
          toast.error(`Failed to get QR code: ${response.error}`)
        } else {
          toast.error('QR code not available. WhatsApp may already be authenticated.')
        }
      }
    })

    socketStore.on('authenticated', () => {
      set({
        isAuthenticated: true,
        qrCode: null,
        error: null
      })
      toast.success('WhatsApp authenticated successfully!')
    })

    socketStore.on('ready', () => {
      set({
        isReady: true,
        connectionState: 'CONNECTED'
      })
      toast.success('WhatsApp is ready to use!')
      
      // Load initial data
      get().loadContacts()
      get().loadChats()
    })

    socketStore.on('disconnected', (reason) => {
      set({
        isReady: false,
        isAuthenticated: false,
        connectionState: 'DISCONNECTED',
        error: reason
      })
      toast.error(`WhatsApp disconnected: ${reason}`)
    })

    socketStore.on('change_state', (state) => {
      set({ connectionState: state })
    })

    socketStore.on('message', (message) => {
      get().addMessage(message)
      
      // Show notification for new messages
      if (!message.fromMe) {
        toast(`New message from ${message.from}`, {
          icon: '💬',
          duration: 3000
        })
      }
    })

    socketStore.on('message_create', (message) => {
      get().addMessage(message)
    })

    // Request initial status
    socketStore.requestStatus()
  },

  // Message management
  addMessage: (message) => {
    set((state) => ({
      messages: [...state.messages, message].sort((a, b) => a.timestamp - b.timestamp)
    }))
  },

  setMessages: (messages) => {
    set({ messages: messages.sort((a, b) => a.timestamp - b.timestamp) })
  },

  clearMessages: () => {
    set({ messages: [] })
  },

  // Chat management
  setCurrentChat: (chatId) => {
    set({ currentChat: chatId })
    
    if (chatId) {
      // Join the chat room for real-time updates
      const socketStore = useSocketStore.getState()
      socketStore.joinRoom(chatId)
      
      // Load messages for this chat
      get().loadMessages(chatId)
    }
  },

  loadMessages: async (chatId, limit = 50) => {
    set({ isLoadingMessages: true })
    
    try {
      const response = await fetch(`/api/messages/${chatId}?limit=${limit}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        set({ messages: data.data || [] })
      } else {
        throw new Error('Failed to load messages')
      }
    } catch (error) {
      console.error('Error loading messages:', error)
      toast.error('Failed to load messages')
    } finally {
      set({ isLoadingMessages: false })
    }
  },

  // Contact management
  loadContacts: async () => {
    set({ isLoadingContacts: true })
    
    try {
      const response = await fetch('/api/contacts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        set({ contacts: data.data || [] })
      } else {
        throw new Error('Failed to load contacts')
      }
    } catch (error) {
      console.error('Error loading contacts:', error)
      toast.error('Failed to load contacts')
    } finally {
      set({ isLoadingContacts: false })
    }
  },

  // Chat management
  loadChats: async () => {
    set({ isLoadingChats: true })
    
    try {
      const response = await fetch('/api/groups', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        set({ chats: data.data || [] })
      } else {
        throw new Error('Failed to load chats')
      }
    } catch (error) {
      console.error('Error loading chats:', error)
      toast.error('Failed to load chats')
    } finally {
      set({ isLoadingChats: false })
    }
  },

  // Actions
  requestQRCode: () => {
    const socketStore = useSocketStore.getState()
    socketStore.requestQR()
  },

  sendMessage: async (chatId, message, options = {}) => {
    try {
      const response = await fetch('/api/messages/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          chatId,
          message,
          options
        })
      })

      if (response.ok) {
        const data = await response.json()
        toast.success('Message sent successfully')
        return data.data
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to send message')
      }
    } catch (error) {
      console.error('Error sending message:', error)
      toast.error(error.message || 'Failed to send message')
      throw error
    }
  },

  sendMediaMessage: async (chatId, file, caption = '') => {
    try {
      const formData = new FormData()
      formData.append('chatId', chatId)
      formData.append('media', file)
      if (caption) {
        formData.append('caption', caption)
      }

      const response = await fetch('/api/messages/send-media', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: formData
      })

      if (response.ok) {
        const data = await response.json()
        toast.success('Media message sent successfully')
        return data.data
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to send media message')
      }
    } catch (error) {
      console.error('Error sending media message:', error)
      toast.error(error.message || 'Failed to send media message')
      throw error
    }
  },

  // Utility methods
  getContactName: (contactId) => {
    const { contacts } = get()
    const contact = contacts.find(c => c.id === contactId)
    return contact?.name || contact?.pushname || contactId
  },

  getChatName: (chatId) => {
    const { chats } = get()
    const chat = chats.find(c => c.id === chatId)
    return chat?.name || chatId
  },

  // Reset store
  reset: () => {
    set({
      isReady: false,
      isAuthenticated: false,
      qrCode: null,
      connectionState: 'DISCONNECTED',
      error: null,
      messages: [],
      currentChat: null,
      contacts: [],
      chats: [],
      isLoadingMessages: false,
      isLoadingContacts: false,
      isLoadingChats: false
    })
  }
}))
