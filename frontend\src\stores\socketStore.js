import { create } from 'zustand'
import { io } from 'socket.io-client'
import toast from 'react-hot-toast'

export const useSocketStore = create((set, get) => ({
  // State
  socket: null,
  isConnected: false,
  connectionError: null,
  lastPing: null,

  // Actions
  connect: () => {
    const { socket: existingSocket } = get()

    // Don't create multiple connections
    if (existingSocket?.connected) {
      return
    }

    // Disconnect existing socket if any
    if (existingSocket) {
      existingSocket.disconnect()
    }

    const token = localStorage.getItem('auth_token')

    // Use the correct WebSocket URL - should connect to the same origin when using proxy
    const wsUrl = window.location.origin

    console.log('Connecting to WebSocket at:', wsUrl)

    const socket = io(wsUrl, {
      auth: {
        token
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 2000,
      reconnectionDelayMax: 10000,
      maxReconnectionAttempts: 5,
      forceNew: false,
      upgrade: true,
      rememberUpgrade: true
    })

    // Connection events
    socket.on('connect', () => {
      console.log('Socket connected:', socket.id)
      set({
        socket,
        isConnected: true,
        connectionError: null,
        lastPing: new Date()
      })
      toast.success('Connected to server')
    })

    socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason)
      set({
        isConnected: false,
        connectionError: reason
      })
      
      if (reason !== 'io client disconnect') {
        toast.error('Disconnected from server')
      }
    })

    socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error)
      set({
        isConnected: false,
        connectionError: error.message
      })
      toast.error('Connection failed')
    })

    socket.on('reconnect', (attemptNumber) => {
      console.log('Socket reconnected after', attemptNumber, 'attempts')
      set({
        isConnected: true,
        connectionError: null,
        lastPing: new Date()
      })
      toast.success('Reconnected to server')
    })

    socket.on('reconnect_error', (error) => {
      console.error('Socket reconnection error:', error)
      set({ connectionError: error.message })
    })

    // Keep track of connection health
    socket.on('pong', () => {
      set({ lastPing: new Date() })
    })

    set({ socket })
  },

  disconnect: () => {
    const { socket } = get()
    
    if (socket) {
      socket.disconnect()
      set({
        socket: null,
        isConnected: false,
        connectionError: null
      })
    }
  },

  // Event subscription helpers
  on: (event, callback) => {
    const { socket } = get()
    if (socket) {
      socket.on(event, callback)
    }
  },

  off: (event, callback) => {
    const { socket } = get()
    if (socket) {
      socket.off(event, callback)
    }
  },

  emit: (event, data) => {
    const { socket, isConnected } = get()
    if (socket && isConnected) {
      socket.emit(event, data)
    } else {
      console.warn('Cannot emit event: socket not connected')
      toast.error('Not connected to server')
    }
  },

  // Utility methods
  joinRoom: (room) => {
    get().emit('join_room', room)
  },

  leaveRoom: (room) => {
    get().emit('leave_room', room)
  },

  requestQR: () => {
    get().emit('request_qr')
  },

  requestStatus: () => {
    get().emit('request_status')
  },

  markAsRead: (chatId, messageId) => {
    get().emit('mark_as_read', { chatId, messageId })
  },

  startTyping: (chatId) => {
    get().emit('typing_start', { chatId })
  },

  stopTyping: (chatId) => {
    get().emit('typing_stop', { chatId })
  },

  // Connection health check
  ping: () => {
    const { socket } = get()
    if (socket) {
      socket.emit('ping')
    }
  },

  getConnectionStatus: () => {
    const { isConnected, connectionError, lastPing } = get()
    return {
      isConnected,
      error: connectionError,
      lastPing,
      isHealthy: isConnected && (!lastPing || Date.now() - lastPing.getTime() < 30000) // 30 seconds
    }
  }
}))

// Auto-ping every 25 seconds to keep connection alive
setInterval(() => {
  const { ping, isConnected } = useSocketStore.getState()
  if (isConnected) {
    ping()
  }
}, 25000)
