const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = require('./src/app');
const WhatsAppService = require('./src/services/whatsappService');
const SchedulerService = require('./src/services/schedulerService');
const logger = require('./src/utils/logger');

const PORT = process.env.PORT || 3001;

// Create HTTP server
const server = http.createServer(app);

// Setup Socket.IO with improved configuration
const io = socketIo(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || "http://localhost:3000",
    methods: ["GET", "POST"]
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  upgradeTimeout: 30000,
  allowUpgrades: true,
  transports: ['websocket', 'polling'],
  allowEIO3: true
});

// Initialize WhatsApp service with Socket.IO
const whatsappService = new WhatsAppService(io);

// Initialize Scheduler service
const schedulerService = new SchedulerService(whatsappService);

// Make services available to routes
app.set('whatsappService', whatsappService);
app.set('schedulerService', schedulerService);

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  socket.on('disconnect', (reason) => {
    logger.info(`Client disconnected: ${socket.id}, reason: ${reason}`);
  });

  socket.on('error', (error) => {
    logger.error(`Socket error for ${socket.id}:`, error);
  });

  // Handle QR code requests
  socket.on('request_qr', () => {
    try {
      const qrCode = whatsappService.getQRCode();
      socket.emit('qr_response', {
        qrCode,
        available: !!qrCode
      });
    } catch (error) {
      logger.error('Error handling QR request:', error);
      socket.emit('qr_response', {
        qrCode: null,
        available: false,
        error: 'Failed to get QR code'
      });
    }
  });

  // Send current WhatsApp status to newly connected client
  try {
    socket.emit('whatsapp_status', {
      isReady: whatsappService.isReady(),
      isAuthenticated: whatsappService.isAuthenticated(),
      hasQRCode: !!whatsappService.getQRCode()
    });
  } catch (error) {
    logger.error('Error sending initial status:', error);
  }
});

// Initialize WhatsApp client
whatsappService.initialize().catch(error => {
  logger.error('Failed to initialize WhatsApp service:', error);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  
  try {
    await whatsappService.destroy();
    schedulerService.destroy();
    server.close(() => {
      logger.info('Server closed');
      process.exit(0);
    });
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  
  try {
    await whatsappService.destroy();
    schedulerService.destroy();
    server.close(() => {
      logger.info('Server closed');
      process.exit(0);
    });
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

// Start server
server.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
});
